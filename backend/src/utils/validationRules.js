/**
 * 统一的express-validator验证规则
 * 提供可复用的验证规则，避免重复定义
 */

const { body, query, param } = require('express-validator');
const { USER_ROLES, CONTRACT_STATUS } = require('./constants');

/**
 * 通用验证规则
 */
const commonValidationRules = {
  // 用户名验证
  username: () => body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),

  // 可选用户名验证
  usernameOptional: () => body('username')
    .optional()
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),

  // 密码验证
  password: () => body('password')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符'),

  // 角色验证
  role: () => body('role')
    .isIn(Object.values(USER_ROLES))
    .withMessage('无效的用户角色'),

  // 可选角色验证
  roleOptional: () => body('role')
    .optional()
    .isIn(Object.values(USER_ROLES))
    .withMessage('无效的用户角色'),

  // 真实姓名验证
  realName: () => body('real_name')
    .optional()
    .isLength({ max: 100 })
    .withMessage('真实姓名不能超过100个字符'),

  // 邮箱验证
  email: () => body('email')
    .optional()
    .isEmail()
    .withMessage('邮箱格式不正确'),

  // 手机号验证
  phone: () => body('phone')
    .optional()
    .matches(/^1[3-9]\d{9}$/)
    .withMessage('手机号格式不正确'),

  // 用户状态验证
  userStatus: () => body('status')
    .optional()
    .isIn(['active', 'inactive', 'banned'])
    .withMessage('无效的用户状态'),

  // 文件名验证
  filename: () => body('filename')
    .notEmpty()
    .withMessage('文件名不能为空'),

  // 文件路径验证
  filePath: () => body('file_path')
    .notEmpty()
    .withMessage('文件路径不能为空'),

  // 文件大小验证
  fileSize: () => body('file_size')
    .isInt({ min: 1 })
    .withMessage('文件大小必须大于0'),

  // 审核级别验证
  reviewLevel: () => body('review_level')
    .isIn(['county_reviewer', 'city_reviewer'])
    .withMessage('请选择有效的审核级别'),

  // 审核员ID验证
  reviewerId: () => body('reviewer_id')
    .isInt({ min: 1 })
    .withMessage('请选择审核员'),

  // 可选审核员ID验证
  reviewerIdOptional: () => body('reviewer_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('请选择审核员'),

  // 提交说明验证
  submitNote: () => body('submit_note')
    .optional()
    .isLength({ max: 500 })
    .withMessage('提交说明不能超过500字符'),

  // 分页参数验证
  page: () => query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),

  pageSize: () => query('pageSize')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),

  // 合同状态验证
  contractStatus: () => query('status')
    .optional()
    .custom((value) => {
      const validValues = [
        ...Object.values(CONTRACT_STATUS),
        'pending_all',
        'approved_all',
        'reviewer_assigned',
        'reviewer_pending',
        'legal_assigned'
      ];
      return validValues.includes(value);
    })
    .withMessage('无效的状态值'),

  // 提交人ID验证
  submitterId: () => query('submitter_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('提交人ID必须是正整数'),

  // 查询审核员ID验证
  reviewerIdQuery: () => query('reviewer_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('审核员ID必须是正整数'),

  // ID参数验证
  idParam: (paramName = 'id') => param(paramName)
    .isInt({ min: 1 })
    .withMessage(`${paramName}必须是正整数`)
};

/**
 * 组合验证规则
 */
const validationRuleGroups = {
  // 用户创建验证规则
  createUser: [
    commonValidationRules.username(),
    commonValidationRules.password(),
    commonValidationRules.role(),
    commonValidationRules.realName(),
    commonValidationRules.email(),
    commonValidationRules.phone()
  ],

  // 用户更新验证规则
  updateUser: [
    commonValidationRules.usernameOptional(),
    commonValidationRules.roleOptional(),
    commonValidationRules.realName(),
    commonValidationRules.email(),
    commonValidationRules.phone(),
    commonValidationRules.userStatus()
  ],

  // 合同提交验证规则
  createContract: [
    commonValidationRules.filename(),
    commonValidationRules.filePath(),
    commonValidationRules.fileSize(),
    commonValidationRules.reviewLevel(),
    commonValidationRules.reviewerId(),
    commonValidationRules.submitNote()
  ],

  // 合同更新验证规则
  updateContract: [
    commonValidationRules.filename().optional(),
    commonValidationRules.filePath().optional(),
    commonValidationRules.fileSize().optional(),
    commonValidationRules.reviewerIdOptional(),
    commonValidationRules.submitNote()
  ],

  // 合同列表查询验证规则
  contractQuery: [
    commonValidationRules.page(),
    commonValidationRules.pageSize(),
    commonValidationRules.contractStatus(),
    commonValidationRules.submitterId(),
    commonValidationRules.reviewerIdQuery()
  ],

  // 分页查询验证规则
  pagination: [
    commonValidationRules.page(),
    commonValidationRules.pageSize()
  ]
};

/**
 * 创建自定义验证规则
 */
const createCustomValidation = {
  /**
   * 创建字符串长度验证
   */
  stringLength: (field, min, max, message) => body(field)
    .isLength({ min, max })
    .withMessage(message || `${field}长度必须在${min}-${max}个字符之间`),

  /**
   * 创建整数验证
   */
  integer: (field, min = 1, message) => body(field)
    .isInt({ min })
    .withMessage(message || `${field}必须是大于等于${min}的整数`),

  /**
   * 创建枚举验证
   */
  enum: (field, values, message) => body(field)
    .isIn(values)
    .withMessage(message || `${field}必须是有效值`),

  /**
   * 创建可选枚举验证
   */
  enumOptional: (field, values, message) => body(field)
    .optional()
    .isIn(values)
    .withMessage(message || `${field}必须是有效值`)
};

module.exports = {
  commonValidationRules,
  validationRuleGroups,
  createCustomValidation
};
