/**
 * JWT错误处理统一工具
 * 提供统一的JWT错误处理逻辑，避免重复代码
 */

const { ResponseUtils } = require('./helpers');
const { HTTP_STATUS, RESPONSE_MESSAGES } = require('./constants');

/**
 * JWT错误处理器
 */
class JWTErrorHandler {
  /**
   * 处理JWT相关错误
   * @param {Error} error - 错误对象
   * @param {Object} res - Express响应对象
   * @returns {boolean} 是否处理了JWT错误
   */
  static handle(error, res) {
    // 处理无效的JWT令牌
    if (error.name === 'JsonWebTokenError') {
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error('无效的访问令牌', HTTP_STATUS.UNAUTHORIZED)
      );
      return true;
    }

    // 处理过期的JWT令牌
    if (error.name === 'TokenExpiredError') {
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error('访问令牌已过期', HTTP_STATUS.UNAUTHORIZED)
      );
      return true;
    }

    // 不是JWT错误，返回false表示未处理
    return false;
  }

  /**
   * 处理JWT相关错误（使用预定义消息）
   * @param {Error} error - 错误对象
   * @param {Object} res - Express响应对象
   * @returns {boolean} 是否处理了JWT错误
   */
  static handleWithMessages(error, res) {
    // 处理无效的JWT令牌
    if (error.name === 'JsonWebTokenError') {
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INVALID_TOKEN, HTTP_STATUS.UNAUTHORIZED)
      );
      return true;
    }

    // 处理过期的JWT令牌
    if (error.name === 'TokenExpiredError') {
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error(RESPONSE_MESSAGES.TOKEN_EXPIRED, HTTP_STATUS.UNAUTHORIZED)
      );
      return true;
    }

    // 不是JWT错误，返回false表示未处理
    return false;
  }

  /**
   * 检查是否为JWT错误
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否为JWT错误
   */
  static isJWTError(error) {
    return error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError';
  }

  /**
   * 获取JWT错误类型
   * @param {Error} error - 错误对象
   * @returns {string|null} 错误类型或null
   */
  static getErrorType(error) {
    if (error.name === 'JsonWebTokenError') {
      return 'INVALID_TOKEN';
    }
    if (error.name === 'TokenExpiredError') {
      return 'EXPIRED_TOKEN';
    }
    return null;
  }

  /**
   * 创建JWT错误响应
   * @param {string} errorType - 错误类型
   * @returns {Object} 错误响应对象
   */
  static createErrorResponse(errorType) {
    switch (errorType) {
      case 'INVALID_TOKEN':
        return {
          status: HTTP_STATUS.UNAUTHORIZED,
          response: ResponseUtils.error('无效的访问令牌', HTTP_STATUS.UNAUTHORIZED)
        };
      case 'EXPIRED_TOKEN':
        return {
          status: HTTP_STATUS.UNAUTHORIZED,
          response: ResponseUtils.error('访问令牌已过期', HTTP_STATUS.UNAUTHORIZED)
        };
      default:
        return {
          status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
          response: ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
        };
    }
  }
}

module.exports = JWTErrorHandler;
