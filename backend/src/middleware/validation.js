/**
 * 统一验证中间件
 * 提供统一的参数验证和错误处理
 */

const { validationResult } = require('express-validator');
const { ResponseUtils } = require('../utils/helpers');
const { HTTP_STATUS } = require('../utils/constants');

/**
 * 统一的验证结果处理中间件
 * 检查express-validator的验证结果，如果有错误则返回统一格式的错误响应
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      ResponseUtils.error('参数验证失败', HTTP_STATUS.BAD_REQUEST, errors.array())
    );
  }
  next();
};

/**
 * 统一的用户状态检查
 * 检查用户是否存在且状态正常
 */
const validateUserStatus = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error('用户未认证', HTTP_STATUS.UNAUTHORIZED)
      );
    }

    // 检查用户状态
    if (req.user.status === 'banned') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('用户已被禁用', HTTP_STATUS.FORBIDDEN)
      );
    }

    next();
  } catch (error) {
    console.error('用户状态验证错误:', error);
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error('服务器内部错误', HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * 统一的ID参数验证
 * 验证路径参数中的ID是否为有效的正整数
 */
const validateIdParam = (paramName = 'id') => {
  return (req, res, next) => {
    const id = parseInt(req.params[paramName]);
    
    if (!id || id <= 0) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseUtils.error(`无效的${paramName}`, HTTP_STATUS.BAD_REQUEST)
      );
    }

    // 将解析后的ID添加到请求对象中
    req.validatedId = id;
    next();
  };
};

/**
 * 统一的分页参数验证和处理
 */
const validatePagination = (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const pageSize = parseInt(req.query.pageSize) || 10;

  // 验证分页参数
  if (page < 1) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      ResponseUtils.error('页码必须大于0', HTTP_STATUS.BAD_REQUEST)
    );
  }

  if (pageSize < 1 || pageSize > 100) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      ResponseUtils.error('每页数量必须在1-100之间', HTTP_STATUS.BAD_REQUEST)
    );
  }

  // 将验证后的分页参数添加到请求对象中
  req.pagination = { page, pageSize };
  next();
};

/**
 * 创建用户信息对象的统一方法
 * 从数据库用户对象创建标准的用户信息对象
 */
const createUserInfo = (user) => {
  if (!user) return null;
  
  return {
    id: user.id,
    username: user.username,
    role: user.role,
    status: user.status,
    avatar: user.avatar
  };
};

/**
 * 统一的资源存在性检查
 * 检查指定的资源是否存在
 */
const validateResourceExists = (Model, resourceName = '资源') => {
  return async (req, res, next) => {
    try {
      const id = req.validatedId || parseInt(req.params.id);
      
      if (!id) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('缺少资源ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      const resource = await Model.findById(id);
      
      if (!resource) {
        return res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseUtils.error(`${resourceName}不存在`, HTTP_STATUS.NOT_FOUND)
        );
      }

      // 将资源添加到请求对象中
      req.resource = resource;
      next();
    } catch (error) {
      console.error(`${resourceName}存在性检查错误:`, error);
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error('服务器内部错误', HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  };
};

module.exports = {
  handleValidationErrors,
  validateUserStatus,
  validateIdParam,
  validatePagination,
  createUserInfo,
  validateResourceExists
};
