/**
 * 认证中间件
 * 处理JWT令牌验证和用户身份认证
 */

const jwt = require('jsonwebtoken');
const { UserModel } = require('../utils/database');
const { JWT_CONFIG, HTTP_STATUS, RESPONSE_MESSAGES } = require('../utils/constants');
const { ResponseUtils, TokenUtils } = require('../utils/helpers');
const JWTErrorHandler = require('../utils/jwtErrorHandler');
const { createUserInfo } = require('./validation');

/**
 * JWT 认证中间件
 * 验证请求头中的 Authorization 令牌
 */
const authenticateToken = async (req, res, next) => {
  try {
    // 获取 Authorization 头
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error(RESPONSE_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED)
      );
    }

    // 验证令牌
    const decoded = TokenUtils.verify(token);

    // 查询用户信息
    const user = await UserModel.findById(decoded.id);

    if (!user) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error(RESPONSE_MESSAGES.USER_NOT_FOUND, HTTP_STATUS.UNAUTHORIZED)
      );
    }

    // 检查用户状态
    if (user.status === 'banned') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error(RESPONSE_MESSAGES.USER_BANNED, HTTP_STATUS.FORBIDDEN)
      );
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: user.id,
      username: user.username,
      role: user.role,
      status: user.status,
      avatar: user.avatar
    };

    next();
  } catch (error) {
    console.error('认证中间件错误:', error);

    // 使用统一的JWT错误处理
    if (JWTErrorHandler.handle(error, res)) {
      return;
    }

    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * 可选认证中间件
 * 如果有令牌则验证，没有令牌则跳过
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = TokenUtils.verify(token);
      const user = await UserModel.findById(decoded.id);

      if (user && user.status === 'active') {
        req.user = {
          id: user.id,
          username: user.username,
          role: user.role,
          status: user.status,
          avatar: user.avatar
        };
      }
    }

    next();
  } catch (error) {
    // 可选认证失败时不阻止请求继续
    console.warn('可选认证失败:', error.message);
    next();
  }
};

/**
 * 刷新令牌中间件
 * 检查令牌是否即将过期，如果是则生成新令牌
 */
const refreshTokenIfNeeded = (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token && req.user) {
      const decoded = TokenUtils.decode(token);
      const now = Math.floor(Date.now() / 1000);
      const timeUntilExpiry = decoded.exp - now;

      // 如果令牌在30分钟内过期，生成新令牌
      if (timeUntilExpiry < 30 * 60) {
        const newToken = TokenUtils.generate({
          id: req.user.id,
          username: req.user.username,
          role: req.user.role
        });

        // 在响应头中返回新令牌
        res.setHeader('X-New-Token', newToken);
      }
    }

    next();
  } catch (error) {
    console.warn('令牌刷新失败:', error.message);
    next();
  }
};

/**
 * 登录限制中间件
 * 防止暴力破解登录
 */
const loginRateLimit = (() => {
  const attempts = new Map();
  const MAX_ATTEMPTS = 5;
  const WINDOW_MS = 15 * 60 * 1000; // 15分钟

  return (req, res, next) => {
    const ip = req.ip || req.connection.remoteAddress;
    const now = Date.now();

    // 清理过期记录
    for (const [key, data] of attempts.entries()) {
      if (now - data.firstAttempt > WINDOW_MS) {
        attempts.delete(key);
      }
    }

    const attemptData = attempts.get(ip);

    if (attemptData) {
      if (attemptData.count >= MAX_ATTEMPTS) {
        const timeLeft = Math.ceil((attemptData.firstAttempt + WINDOW_MS - now) / 1000 / 60);
        return res.status(HTTP_STATUS.TOO_MANY_REQUESTS).json(
          ResponseUtils.error(`登录尝试次数过多，请${timeLeft}分钟后再试`, 429)
        );
      }

      attemptData.count++;
    } else {
      attempts.set(ip, {
        count: 1,
        firstAttempt: now
      });
    }

    // 登录成功后清除记录
    req.clearLoginAttempts = () => {
      attempts.delete(ip);
    };

    next();
  };
})();

/**
 * 验证用户会话
 * 检查用户会话是否有效
 */
const validateSession = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error(RESPONSE_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED)
      );
    }

    // 重新查询用户信息以确保数据最新
    const user = await UserModel.findById(req.user.id);

    if (!user) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error(RESPONSE_MESSAGES.USER_NOT_FOUND, HTTP_STATUS.UNAUTHORIZED)
      );
    }

    if (user.status === 'banned') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error(RESPONSE_MESSAGES.USER_BANNED, HTTP_STATUS.FORBIDDEN)
      );
    }

    // 更新用户信息 - 使用统一的用户信息构建方法
    req.user = createUserInfo(user);

    next();
  } catch (error) {
    console.error('会话验证错误:', error);
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * 文件访问认证中间件 (支持URL token参数)
 * 用于文件预览和下载，支持从Authorization头或URL参数获取token
 */
const authenticateFileAccess = async (req, res, next) => {
  try {
    let token = null;

    // 首先尝试从 Authorization 头获取 token
    const authHeader = req.headers['authorization'];
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
    }

    // 如果头部没有token，尝试从URL参数获取
    if (!token && req.query.token) {
      token = req.query.token;
    }

    if (!token) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error(RESPONSE_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED)
      );
    }

    // 验证令牌
    const decoded = TokenUtils.verify(token);

    // 查询用户信息
    const user = await UserModel.findById(decoded.id);

    if (!user) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error(RESPONSE_MESSAGES.USER_NOT_FOUND, HTTP_STATUS.UNAUTHORIZED)
      );
    }


    // 检查用户状态
    if (user.status === 'banned') {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error(RESPONSE_MESSAGES.USER_BANNED, HTTP_STATUS.FORBIDDEN)
      );
    }

    // 将用户信息添加到请求对象 - 使用统一的用户信息构建方法
    req.user = createUserInfo(user);

    next();
  } catch (error) {
    console.error('文件访问认证错误:', error);

    // 使用统一的JWT错误处理
    if (JWTErrorHandler.handleWithMessages(error, res)) {
      return;
    }

    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
};

module.exports = {
  authenticateToken,
  authenticateFileAccess,
  optionalAuth,
  refreshTokenIfNeeded,
  loginRateLimit,
  validateSession
};
