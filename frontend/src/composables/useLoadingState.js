/**
 * 统一加载状态管理 Composable
 * 提供一致的加载状态管理和错误处理
 */

import { ref, computed } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';

/**
 * 基础加载状态管理
 */
export function useLoading(initialState = false) {
  const loading = ref(initialState);
  const error = ref(null);

  /**
   * 设置加载状态
   */
  const setLoading = (state) => {
    loading.value = state;
    if (state) {
      error.value = null; // 开始加载时清除错误
    }
  };

  /**
   * 设置错误状态
   */
  const setError = (err) => {
    error.value = err;
    loading.value = false;
  };

  /**
   * 清除状态
   */
  const clear = () => {
    loading.value = false;
    error.value = null;
  };

  /**
   * 包装异步函数，自动管理加载状态
   */
  const withLoading = async (asyncFn, ...args) => {
    setLoading(true);
    try {
      const result = await asyncFn(...args);
      setLoading(false);
      return result;
    } catch (err) {
      setError(err);
      throw err;
    }
  };

  return {
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    isLoading: computed(() => loading.value),
    hasError: computed(() => !!error.value),
    setLoading,
    setError,
    clear,
    withLoading
  };
}

/**
 * 全局加载状态管理
 */
export function useGlobalLoading() {
  let loadingInstance = null;

  /**
   * 显示全局加载
   */
  const showGlobalLoading = (text = '加载中...', options = {}) => {
    if (loadingInstance) {
      return loadingInstance;
    }

    const defaultOptions = {
      text,
      background: 'rgba(0, 0, 0, 0.7)',
      ...options
    };

    loadingInstance = ElLoading.service(defaultOptions);
    return loadingInstance;
  };

  /**
   * 隐藏全局加载
   */
  const hideGlobalLoading = () => {
    if (loadingInstance) {
      loadingInstance.close();
      loadingInstance = null;
    }
  };

  /**
   * 包装异步函数，显示全局加载
   */
  const withGlobalLoading = async (asyncFn, text = '处理中...', ...args) => {
    showGlobalLoading(text);
    try {
      const result = await asyncFn(...args);
      hideGlobalLoading();
      return result;
    } catch (err) {
      hideGlobalLoading();
      throw err;
    }
  };

  return {
    showGlobalLoading,
    hideGlobalLoading,
    withGlobalLoading
  };
}

/**
 * API请求加载状态管理
 */
export function useApiLoading() {
  const { loading, error, setLoading, setError, clear, withLoading } = useLoading();

  /**
   * 包装API调用，自动处理加载状态和错误
   */
  const apiCall = async (apiFunction, options = {}) => {
    const {
      showMessage = true,
      successMessage = '',
      errorMessage = '',
      showGlobalLoading = false,
      loadingText = '请求处理中...'
    } = options;

    let globalLoading = null;
    
    try {
      setLoading(true);
      
      if (showGlobalLoading) {
        globalLoading = ElLoading.service({
          text: loadingText,
          background: 'rgba(0, 0, 0, 0.7)'
        });
      }

      const result = await apiFunction();
      
      setLoading(false);
      
      if (globalLoading) {
        globalLoading.close();
      }

      if (showMessage && successMessage) {
        ElMessage.success(successMessage);
      }

      return result;
    } catch (err) {
      setError(err);
      
      if (globalLoading) {
        globalLoading.close();
      }

      if (showMessage) {
        const message = errorMessage || err.message || '操作失败';
        ElMessage.error(message);
      }

      throw err;
    }
  };

  return {
    loading,
    error,
    isLoading: computed(() => loading.value),
    hasError: computed(() => !!error.value),
    apiCall,
    clear
  };
}

/**
 * 分页数据加载状态管理
 */
export function usePaginatedLoading() {
  const { loading, error, setLoading, setError, clear } = useLoading();
  const refreshing = ref(false);

  /**
   * 加载数据
   */
  const loadData = async (loadFunction, showRefreshIndicator = false) => {
    try {
      if (showRefreshIndicator) {
        refreshing.value = true;
      } else {
        setLoading(true);
      }

      const result = await loadFunction();
      
      setLoading(false);
      refreshing.value = false;
      
      return result;
    } catch (err) {
      setError(err);
      refreshing.value = false;
      throw err;
    }
  };

  /**
   * 刷新数据
   */
  const refreshData = async (loadFunction) => {
    return loadData(loadFunction, true);
  };

  return {
    loading,
    error,
    refreshing: computed(() => refreshing.value),
    isLoading: computed(() => loading.value),
    isRefreshing: computed(() => refreshing.value),
    hasError: computed(() => !!error.value),
    loadData,
    refreshData,
    clear
  };
}

/**
 * 表单提交加载状态管理
 */
export function useFormLoading() {
  const { loading, error, setLoading, setError, clear } = useLoading();

  /**
   * 提交表单
   */
  const submitForm = async (submitFunction, options = {}) => {
    const {
      successMessage = '操作成功',
      errorMessage = '',
      showLoading = true
    } = options;

    try {
      if (showLoading) {
        setLoading(true);
      }

      const result = await submitFunction();
      
      setLoading(false);
      
      if (successMessage) {
        ElMessage.success(successMessage);
      }

      return result;
    } catch (err) {
      setError(err);
      
      const message = errorMessage || err.message || '提交失败';
      ElMessage.error(message);
      
      throw err;
    }
  };

  return {
    loading,
    error,
    isSubmitting: computed(() => loading.value),
    hasError: computed(() => !!error.value),
    submitForm,
    clear
  };
}

export default {
  useLoading,
  useGlobalLoading,
  useApiLoading,
  usePaginatedLoading,
  useFormLoading
};
