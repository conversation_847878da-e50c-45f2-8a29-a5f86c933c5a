/**
 * 认证相关 API
 * 处理用户登录、登出、注册等认证功能
 */

import api from "./request";

// 认证 API 接口
export const authAPI = {
  /**
   * 用户登录
   * @param {Object} credentials - 登录凭据
   * @param {string} credentials.username - 用户名
   * @param {string} credentials.password - 密码
   * @returns {Promise} 登录结果
   */
  login(credentials) {
    return api.post("/auth/login", credentials);
  },

  /**
   * 用户登出
   * @returns {Promise} 登出结果
   */
  logout() {
    return api.post("/auth/logout");
  },

  /**
   * 获取当前用户信息
   * @returns {Promise} 用户信息
   */
  getProfile() {
    return api.get("/auth/profile");
  },

  /**
   * 修改密码
   * @param {Object} passwordData - 密码数据
   * @param {string} passwordData.currentPassword - 当前密码
   * @param {string} passwordData.newPassword - 新密码
   * @param {string} passwordData.confirmPassword - 确认密码
   * @returns {Promise} 修改结果
   */
  changePassword(passwordData) {
    return api.put("/auth/password", passwordData);
  },

  /**
   * 验证令牌有效性
   * @returns {Promise} 验证结果
   */
  verifyToken() {
    return api.get("/auth/verify");
  },

  /**
   * 刷新令牌
   * @returns {Promise} 新令牌
   */
  refreshToken() {
    return api.post("/auth/refresh");
  },

  /**
   * 获取用户权限
   * @param {number} userId - 用户ID
   * @returns {Promise} 用户权限列表
   */
  getUserPermissions(userId) {
    return api.get(`/auth/permissions/${userId}`);
  },

  /**
   * 获取用户详细信息
   * @param {number} userId - 用户ID
   * @returns {Promise} 用户详细信息
   */
  getUserProfile(userId) {
    return api.get(`/users/${userId}`);
  },

  /**
   * 更新用户资料
   * @param {Object} userData - 用户数据
   * @returns {Promise} 更新结果
   */
  updateProfile(userData) {
    return api.put("/auth/profile", userData);
  },

  /**
   * 清理用户缓存
   * @returns {Promise} 清理结果
   */
  clearUserCache() {
    return api.post("/auth/clear-cache");
  },
};

export default authAPI;
