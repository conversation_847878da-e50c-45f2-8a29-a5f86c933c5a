/**
 * 用户状态管理 Pinia Store
 * 管理用户认证、个人资料、角色和权限
 */

import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { ElMessage } from "element-plus";
import { authAPI } from "@/api/auth";
import { dashboardAPI } from "@/api/dashboard";
import { clearAllTabsData } from "@/composables/useTabs";
import { clearDashboardData } from "@/composables/useDashboard";
// 缓存工具导入已移除，系统直接从API获取数据

export const useUserStore = defineStore("user", () => {
  // ===== 状态 =====
  const user = ref(null);
  const token = ref(localStorage.getItem("token") || null);
  const loading = ref(false);
  const permissions = ref([]);
  const lastLoginTime = ref(null);

  // ===== 计算属性 (Getters) =====
  const isAuthenticated = computed(() => !!token.value && !!user.value);

  const userRole = computed(() => user.value?.role || null);

  const userName = computed(() => user.value?.username || "");

  const userDisplayName = computed(
    () => user.value?.real_name || user.value?.username || "",
  );

  const isAdmin = computed(() => userRole.value === "admin");

  const isReviewer = computed(() =>
    ["county_reviewer", "city_reviewer"].includes(userRole.value),
  );

  const isEmployee = computed(() => userRole.value === "employee");

  const hasPermission = computed(() => (permission) => {
    if (isAdmin.value) return true;
    return permissions.value.includes(permission);
  });

  const canAccessRoute = computed(() => (routeRole) => {
    if (!routeRole) return true;
    if (Array.isArray(routeRole)) {
      return routeRole.includes(userRole.value);
    }
    return userRole.value === routeRole;
  });

  // ===== 操作方法 (Actions) =====

  /**
   * 初始化用户状态
   * 从localStorage恢复用户信息并验证token
   */
  const initAuth = async () => {
    try {
      const savedToken = localStorage.getItem("token");
      const savedUser = localStorage.getItem("user");

      // 检查token和user的一致性
      if (savedToken && savedUser) {
        token.value = savedToken;
        user.value = JSON.parse(savedUser);

        // 验证token是否仍然有效
        const isValid = await verifyToken();
        if (!isValid) {
          console.warn("Token验证失败，清除认证状态");
          clearAuth();
        }
      } else if (savedUser && !savedToken) {
        // 如果只有user信息没有token，说明状态不一致，清除残留数据
        console.warn("发现不一致的认证状态：有用户信息但无token，清除残留数据");
        localStorage.removeItem("user");
        localStorage.removeItem("userInfo");
        clearAuth();
      } else if (savedToken && !savedUser) {
        // 如果只有token没有user信息，也是不一致状态
        console.warn("发现不一致的认证状态：有token但无用户信息，清除残留数据");
        localStorage.removeItem("token");
        clearAuth();
      }
    } catch (error) {
      console.warn("用户状态初始化失败:", error);
      clearAuth();
    }
  };

  /**
   * 用户登录
   * @param {Object} credentials - 登录凭据
   * @param {string} credentials.username - 用户名
   * @param {string} credentials.password - 密码
   * @returns {Promise<boolean>} 登录是否成功
   */
  const login = async (credentials) => {
    try {
      loading.value = true;

      // 1. 保存旧用户信息（用于清理旧缓存）
      const oldUserId = user.value?.id;
      const oldUserRole = user.value?.role;
      const oldToken = token.value;

      const response = await authAPI.login(credentials);

      if (response.success) {
        console.log(
          `✅ 登录成功: 新用户=${response.data.user.id}(${response.data.user.role})`,
        );

        // 2. 先清理旧用户的缓存（在设置新用户信息之前）
        if (oldUserId && oldToken) {
          // 清理旧用户的前端缓存
          try {
            // 缓存清理代码已移除，系统直接从API获取最新数据
            console.log(`✅ 已清理旧用户前端缓存`);
          } catch (error) {
            console.warn("❌ 清理旧用户前端缓存失败:", error);
          }

          // 缓存系统已移除，无需清理后端缓存
        } else {
          // 首次登录，清理所有可能的残留缓存
          try {
            // 缓存清理代码已移除，系统直接从API获取最新数据
          } catch (error) {
            console.warn("❌ 清理残留缓存失败:", error);
          }
        }

        // 3. 设置新用户信息和token
        setUser(response.data.user);
        setToken(response.data.token);

        // 4. 获取用户权限
        await fetchUserPermissions();

        console.log(
          `✅ 用户登录完成: ${response.data.user.id}(${response.data.user.role})`,
        );
        ElMessage.success(response.message || "登录成功");
        return true;
      }

      return false;
    } catch (error) {
      console.error("❌ 登录失败:", error);
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 用户登出
   * @param {boolean} showMessage - 是否显示登出消息
   */
  const logout = async (showMessage = true) => {
    try {
      loading.value = true;

      // 保存当前用户信息（用于清理缓存）
      const currentUserId = user.value?.id;
      const currentUserRole = user.value?.role;
      const currentToken = token.value;

      // 调用后端登出接口
      if (currentToken) {
        try {
          await authAPI.logout();
        } catch (error) {
          console.warn("❌ 后端登出失败:", error);
        }

        // 缓存系统已移除，无需清理dashboard缓存
      }

      // 清除本地状态（传入用户ID确保正确清理）
      clearAuth();

      // 清除tabs相关数据
      try {
        clearAllTabsData();
      } catch (error) {
        console.warn("❌ 清除tabs数据失败:", error);
      }

      // 清除dashboard相关数据
      try {
        clearDashboardData();
      } catch (error) {
        console.warn("❌ 清除dashboard数据失败:", error);
      }

      if (showMessage) {
        ElMessage.success("已安全退出");
      }
    } catch (error) {
      console.error("登出失败:", error);
    } finally {
      loading.value = false;
    }
  };

  /**
   * 验证token有效性
   */
  const verifyToken = async () => {
    try {
      if (!token.value) return false;

      const response = await authAPI.verifyToken();

      if (response.success) {
        // 更新用户信息
        if (response.data.user) {
          setUser(response.data.user);
        }
        return true;
      } else {
        clearAuth();
        return false;
      }
    } catch (error) {
      console.warn("Token验证失败:", error);
      clearAuth();
      return false;
    }
  };

  /**
   * 获取用户权限
   */
  const fetchUserPermissions = async () => {
    try {
      if (!user.value?.id) return;

      const response = await authAPI.getUserPermissions(user.value.id);

      if (response.success) {
        permissions.value = response.data.permissions || [];
      }
    } catch (error) {
      console.warn("获取用户权限失败:", error);
      permissions.value = [];
    }
  };

  /**
   * 获取用户详细信息
   */
  const fetchUserProfile = async () => {
    try {
      if (!user.value?.id) return;

      loading.value = true;
      const response = await authAPI.getUserProfile(user.value.id);

      if (response.success) {
        setUser(response.data.user);
      }
    } catch (error) {
      console.error("获取用户信息失败:", error);
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新用户信息
   * @param {Object} userData - 用户数据
   */
  const updateUserProfile = async (userData) => {
    try {
      loading.value = true;

      const response = await authAPI.updateProfile(userData);

      if (response.success) {
        setUser(response.data.user);
        ElMessage.success("个人信息更新成功");
        return true;
      }

      return false;
    } catch (error) {
      console.error("更新用户信息失败:", error);
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 修改密码
   * @param {Object} passwordData - 密码数据
   */
  const changePassword = async (passwordData) => {
    try {
      loading.value = true;

      const response = await authAPI.changePassword(passwordData);

      if (response.success) {
        ElMessage.success("密码修改成功");
        return true;
      }

      return false;
    } catch (error) {
      console.error("修改密码失败:", error);
      return false;
    } finally {
      loading.value = false;
    }
  };

  // ===== 辅助方法 =====

  /**
   * 设置用户信息
   * @param {Object} userData - 用户数据
   */
  const setUser = (userData) => {
    user.value = userData;
    lastLoginTime.value = new Date().toISOString();

    // 同步到localStorage
    if (userData) {
      localStorage.setItem("user", JSON.stringify(userData));
    } else {
      localStorage.removeItem("user");
    }
  };

  /**
   * 更新用户头像
   * @param {string} avatarPath - 头像路径
   */
  const updateAvatar = (avatarPath) => {
    if (user.value) {
      user.value.avatar = avatarPath;
      // 同步到localStorage
      localStorage.setItem("user", JSON.stringify(user.value));
    }
  };

  /**
   * 设置token
   * @param {string} tokenValue - token值
   */
  const setToken = (tokenValue) => {
    token.value = tokenValue;

    // 同步到localStorage
    if (tokenValue) {
      localStorage.setItem("token", tokenValue);
    } else {
      localStorage.removeItem("token");
    }
  };

  /**
   * 清除认证信息 - 使用统一缓存管理器
   */
  const clearAuth = () => {
    // 保存当前用户ID用于后续的缓存清理
    const currentUserId = user.value?.id;
    const currentUserRole = user.value?.role;

    // 设置登出状态标志，防止新的API请求
    window.isLoggingOut = true;

    // 取消所有待处理的请求，避免401错误
    try {
      if (window.cancelAllRequests) {
        window.cancelAllRequests();
      }
    } catch (error) {
      console.warn("❌ 取消请求失败:", error);
    }

    // 立即清除关键的认证状态，避免竞态条件
    user.value = null;
    token.value = null;
    permissions.value = [];
    lastLoginTime.value = null;

    // 立即清除localStorage中的认证信息
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    localStorage.removeItem("userInfo");

    // 异步执行缓存清理，避免阻塞主线程
    setTimeout(() => {
      // 使用统一缓存管理器清理用户缓存
      try {
        // 缓存清理代码已移除，直接调用降级清理方式
        // 传递用户信息给清理函数
        fallbackClearAuthWithUserInfo(currentUserId, currentUserRole);
      } catch (error) {
        console.error("❌ 统一缓存清理失败:", error);

        // 降级到传统清理方式
        fallbackClearAuthWithUserInfo(currentUserId, currentUserRole);
      }

      // 验证缓存清理结果
      validateCacheClearing(currentUserId);

      // 清除登出状态标志
      window.isLoggingOut = false;
    }, 100);
  };

  /**
   * 降级缓存清理方式（带用户信息）
   */
  const fallbackClearAuthWithUserInfo = (userId, userRole) => {
    console.warn("🔄 使用降级缓存清理方式", { userId, userRole });

    // 清除localStorage中的所有用户相关数据
    const legacyKeys = [
      "token",
      "user",
      "app-settings",
      "sidebar-collapsed",
      "contract-review-tabs",
      "contract-review-active-tab",
    ];

    legacyKeys.forEach((key) => {
      localStorage.removeItem(key);
    });

    // 清除所有可能的缓存数据
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (
        key &&
        (key.startsWith("contract-") ||
          key.startsWith("user-") ||
          key.startsWith("dashboard-") ||
          key.startsWith("cache-") ||
          key.startsWith("app-") ||
          key.startsWith("review-") ||
          key.startsWith("stats-") ||
          key.startsWith("page-"))
      ) {
        keysToRemove.push(key);
      }

      // 清理新格式的缓存键：tabs:u{userId}:r{role}:state 等
      if (key && key.includes(":")) {
        // 如果有当前用户ID，只清理当前用户的缓存
        if (userId) {
          const userSpecificPatterns = [
            new RegExp(`^tabs:u${userId}:r\\w+:`),
            new RegExp(`^user:${userId}:`),
            new RegExp(`^contract:${userId}:`),
            new RegExp(`^search:${userId}:`),
            new RegExp(`^stats:${userId}:`),
            new RegExp(`^settings:${userId}:`),
          ];

          if (userSpecificPatterns.some(pattern => pattern.test(key))) {
            keysToRemove.push(key);
          }
        } else {
          // 如果没有用户ID，清理所有匹配的缓存项
          const patterns = [
            /^tabs:u\d+:r\w+:/,           // tabs:u{userId}:r{role}:*
            /^user:\d+:/,                 // user:{userId}:*
            /^contract:\d+:/,             // contract:{userId}:*
            /^search:\d+:/,               // search:{userId}:*
            /^stats:\d+:/,                // stats:{userId}:*
            /^settings:\d+:/,             // settings:{userId}:*
          ];

          if (patterns.some(pattern => pattern.test(key))) {
            keysToRemove.push(key);
          }
        }
      }
    }

    // 删除找到的缓存键
    keysToRemove.forEach((key) => {
      localStorage.removeItem(key);
      console.log(`🔄 清理缓存项: ${key}`);
    });

    // 清除sessionStorage中的数据
    try {
      sessionStorage.clear();
    } catch (error) {
      console.warn("清除sessionStorage失败:", error);
    }
  };

  /**
   * 降级缓存清理方式（兼容性保障）
   */
  const fallbackClearAuth = () => {
    console.warn("🔄 使用降级缓存清理方式");

    // 获取当前用户ID用于清理用户特定的缓存
    const currentUserId = user.value?.id;

    // 清除localStorage中的所有用户相关数据
    const legacyKeys = [
      "token",
      "user",
      "app-settings",
      "sidebar-collapsed",
      "contract-review-tabs",
      "contract-review-active-tab",
    ];

    legacyKeys.forEach((key) => {
      localStorage.removeItem(key);
    });

    // 清除所有可能的缓存数据
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (
        key &&
        (key.startsWith("contract-") ||
          key.startsWith("user-") ||
          key.startsWith("dashboard-") ||
          key.startsWith("cache-") ||
          key.startsWith("app-") ||
          key.startsWith("review-") ||
          key.startsWith("stats-") ||
          key.startsWith("page-"))
      ) {
        keysToRemove.push(key);
      }

      // 清理新格式的缓存键：tabs:u{userId}:r{role}:state 等
      if (key && key.includes(":")) {
        const patterns = [
          /^tabs:u\d+:r\w+:/,           // tabs:u{userId}:r{role}:*
          /^user:\d+:/,                 // user:{userId}:*
          /^contract:\d+:/,             // contract:{userId}:*
          /^search:\d+:/,               // search:{userId}:*
          /^stats:\d+:/,                // stats:{userId}:*
          /^settings:\d+:/,             // settings:{userId}:*
        ];

        // 如果有当前用户ID，只清理当前用户的缓存
        if (currentUserId) {
          const userSpecificPatterns = [
            new RegExp(`^tabs:u${currentUserId}:r\\w+:`),
            new RegExp(`^user:${currentUserId}:`),
            new RegExp(`^contract:${currentUserId}:`),
            new RegExp(`^search:${currentUserId}:`),
            new RegExp(`^stats:${currentUserId}:`),
            new RegExp(`^settings:${currentUserId}:`),
          ];

          if (userSpecificPatterns.some(pattern => pattern.test(key))) {
            keysToRemove.push(key);
          }
        } else {
          // 如果没有用户ID，清理所有匹配的缓存项
          if (patterns.some(pattern => pattern.test(key))) {
            keysToRemove.push(key);
          }
        }
      }
    }

    // 删除找到的缓存键
    keysToRemove.forEach((key) => {
      localStorage.removeItem(key);
    });

    // 清除sessionStorage中的数据
    try {
      sessionStorage.clear();
    } catch (error) {
      console.warn("清除sessionStorage失败:", error);
    }
  };

  /**
   * 验证缓存清理结果
   */
  const validateCacheClearing = (userId = null) => {
    try {
      // 使用传入的用户ID或当前用户ID
      const currentUserId = userId || user.value?.id;

      // 检查关键缓存是否已清理
      const criticalKeys = ["token", "user"];
      const remainingKeys = criticalKeys.filter(
        (key) => localStorage.getItem(key) !== null,
      );

      if (remainingKeys.length > 0) {
        console.warn("⚠️ 发现未清理的关键缓存:", remainingKeys);
        // 强制清理剩余的关键缓存
        remainingKeys.forEach((key) => localStorage.removeItem(key));
      }

      // 统计剩余的当前用户相关缓存项
      let remainingCacheCount = 0;
      const remainingCacheKeys = [];

      const legacyPatterns = [
        /^(contract|user|dashboard|cache|app|review|stats|page)-/,
      ];

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (!key) continue;

        // 检查传统格式的缓存项
        if (legacyPatterns.some((pattern) => pattern.test(key))) {
          remainingCacheCount++;
          remainingCacheKeys.push(key);
          continue;
        }

        // 检查新格式的缓存项，只统计当前用户的
        if (key.includes(":") && currentUserId) {
          const userSpecificPatterns = [
            new RegExp(`^tabs:u${currentUserId}:r\\w+:`),
            new RegExp(`^user:${currentUserId}:`),
            new RegExp(`^contract:${currentUserId}:`),
            new RegExp(`^search:${currentUserId}:`),
            new RegExp(`^stats:${currentUserId}:`),
            new RegExp(`^settings:${currentUserId}:`),
          ];

          if (userSpecificPatterns.some(pattern => pattern.test(key))) {
            remainingCacheCount++;
            remainingCacheKeys.push(key);
          }
        }
      }

      if (remainingCacheCount > 0) {
        console.warn(`⚠️ 仍有 ${remainingCacheCount} 个缓存项未清理:`, remainingCacheKeys);

        // 尝试强制清理剩余的缓存项
        remainingCacheKeys.forEach((key) => {
          try {
            localStorage.removeItem(key);
            console.log(`🔄 强制清理缓存项: ${key}`);
          } catch (error) {
            console.warn(`❌ 清理缓存项失败: ${key}`, error);
          }
        });
      } else {
        console.log("✅ 所有缓存项已成功清理");
      }

      return remainingCacheCount === 0;
    } catch (error) {
      console.error("❌ 缓存清理验证失败:", error);
      return false;
    }
  };

  // 返回store的公共接口
  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    loading: readonly(loading),
    permissions: readonly(permissions),
    lastLoginTime: readonly(lastLoginTime),

    // 计算属性
    isAuthenticated,
    userRole,
    userName,
    userDisplayName,
    isAdmin,
    isReviewer,
    isEmployee,
    hasPermission,
    canAccessRoute,

    // 操作方法
    initAuth,
    login,
    logout,
    verifyToken,
    fetchUserPermissions,
    fetchUserProfile,
    updateUserProfile,
    changePassword,
    setUser,
    setToken,
    updateAvatar,
    clearAuth,

    // 缓存管理方法
    validateCacheClearing,
    fallbackClearAuth,
  };
});
