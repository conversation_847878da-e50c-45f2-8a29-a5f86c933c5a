<template>
  <div class="avatar-uploader" :class="{ disabled }">
    <!-- 头像显示区域 -->
    <div class="avatar-display" @click="triggerFileSelect">
      <div class="avatar-container">
        <img
          v-if="currentAvatar"
          :src="currentAvatar"
          :alt="altText"
          class="avatar-image"
        />
        <div v-else class="avatar-placeholder">
          <el-icon :size="32">
            <User />
          </el-icon>
          <span class="placeholder-text">{{ placeholderText }}</span>
        </div>

        <!-- 悬停遮罩 -->
        <div class="avatar-overlay">
          <el-icon :size="20">
            <Camera />
          </el-icon>
          <span class="overlay-text">{{ overlayText }}</span>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 裁剪对话框 -->
    <el-dialog
      v-model="cropDialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="avatar-crop-dialog"
      @close="handleDialogClose"
    >
      <div class="crop-container">
        <!-- 左侧裁剪区域 -->
        <div class="crop-section">
          <div class="crop-area">
            <canvas
              ref="cropCanvasRef"
              class="crop-canvas"
              @mousedown="startCrop"
              @mousemove="updateCrop"
              @mouseup="endCrop"
              @touchstart="startCrop"
              @touchmove="updateCrop"
              @touchend="endCrop"
            ></canvas>
          </div>
          
          <!-- 工具栏 -->
          <div class="crop-tools">
            <div class="tool-group">
              <span class="tool-label">缩放</span>
              <el-slider
                v-model="cropState.scale"
                :min="0.1"
                :max="3"
                :step="0.1"
                class="scale-slider"
                @input="updatePreview"
              />
            </div>
            <div class="tool-group">
              <span class="tool-label">旋转</span>
              <div class="rotation-controls">
                <el-button size="small" @click="rotate(-90)">
                  <el-icon><RefreshLeft /></el-icon>
                </el-button>
                <el-button size="small" @click="rotate(90)">
                  <el-icon><RefreshRight /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧预览区域 -->
        <div class="preview-section">
          <h4 class="preview-title">预览效果</h4>
          
          <div class="preview-grid">
            <div class="preview-item">
              <span class="preview-label">头像预览</span>
              <canvas
                ref="previewLargeRef"
                class="preview-circle large"
                width="100"
                height="100"
              ></canvas>
              <span class="preview-size">100×100px</span>
            </div>
            
            <div class="preview-item">
              <span class="preview-label">方形预览</span>
              <canvas
                ref="previewMediumRef"
                class="preview-circle medium"
                width="100"
                height="100"
              ></canvas>
              <span class="preview-size">100×100px</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-info">
            <el-icon><InfoFilled /></el-icon>
            <span>建议上传正方形图片，获得最佳显示效果</span>
          </div>
          
          <div class="footer-actions">
            <el-button @click="handleCancel">取消</el-button>
            <el-button @click="resetCrop" :icon="RefreshRight">重置</el-button>
            <el-button 
              type="primary" 
              @click="handleConfirm" 
              :loading="processing"
              :icon="Check"
            >
              {{ processing ? "处理中..." : "确认上传" }}
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onBeforeUnmount, watch } from "vue";
import { ElMessage } from "element-plus";
import {
  User,
  Camera,
  RefreshLeft,
  RefreshRight,
  Check,
  InfoFilled,
} from "@element-plus/icons-vue";

// Props定义
const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  avatarSize: {
    type: Number,
    default: 100,
  },
  outputSize: {
    type: Number,
    default: 200,
  },
  outputQuality: {
    type: Number,
    default: 0.9,
  },
  outputFormat: {
    type: String,
    default: "image/jpeg",
  },
  maxFileSize: {
    type: Number,
    default: 5 * 1024 * 1024, // 5MB
  },
  placeholderText: {
    type: String,
    default: "点击上传头像",
  },
  overlayText: {
    type: String,
    default: "更换头像",
  },
  dialogTitle: {
    type: String,
    default: "裁剪头像",
  },
  altText: {
    type: String,
    default: "用户头像",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

// Emits定义
const emit = defineEmits([
  "update:modelValue",
  "file-select",
  "crop-complete",
  "crop-cancel",
]);

// 响应式数据
const fileInputRef = ref(null);
const cropCanvasRef = ref(null);
const previewLargeRef = ref(null);
const previewMediumRef = ref(null);

const currentAvatar = ref(props.modelValue);
const cropDialogVisible = ref(false);
const originalImageUrl = ref("");
const processing = ref(false);
const originalImage = ref(null);

// 裁剪状态
const cropState = reactive({
  isDragging: false,
  isResizing: false,
  resizeType: "",
  startX: 0,
  startY: 0,
  cropX: 50,
  cropY: 50,
  cropWidth: 200,
  cropHeight: 200,
  scale: 1,
  rotation: 0,
});

// 触发文件选择
const triggerFileSelect = () => {
  if (props.disabled) return;
  fileInputRef.value?.click();
};

// 处理文件选择
const handleFileSelect = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 验证文件类型
  if (!file.type.startsWith("image/")) {
    ElMessage.error("请选择图片文件");
    return;
  }

  // 验证文件大小
  if (file.size > props.maxFileSize) {
    const maxSizeMB = (props.maxFileSize / (1024 * 1024)).toFixed(1);
    ElMessage.error(`文件大小不能超过 ${maxSizeMB}MB`);
    return;
  }

  // 读取文件并显示裁剪对话框
  const reader = new FileReader();
  reader.onload = (e) => {
    originalImageUrl.value = e.target.result;
    cropDialogVisible.value = true;

    nextTick(() => {
      initCropper();
    });
  };
  reader.onerror = () => {
    ElMessage.error("文件读取失败");
  };
  reader.readAsDataURL(file);

  // 触发文件选择事件
  emit("file-select", file);

  // 清空input值，允许重复选择同一文件
  event.target.value = "";
};

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    currentAvatar.value = newValue;
  }
);

// 初始化裁剪器
const initCropper = () => {
  if (!cropCanvasRef.value || !originalImageUrl.value) return;

  const canvas = cropCanvasRef.value;
  const ctx = canvas.getContext("2d");

  const img = new Image();
  img.onload = () => {
    originalImage.value = img;

    // 设置画布尺寸
    const maxWidth = 400;
    const maxHeight = 300;
    const ratio = Math.min(maxWidth / img.width, maxHeight / img.height);

    canvas.width = img.width * ratio;
    canvas.height = img.height * ratio;

    // 初始化裁剪区域
    const size = Math.min(canvas.width, canvas.height) * 0.6;
    cropState.cropX = (canvas.width - size) / 2;
    cropState.cropY = (canvas.height - size) / 2;
    cropState.cropWidth = size;
    cropState.cropHeight = size;
    cropState.scale = 1;
    cropState.rotation = 0;

    drawCanvas();
    updatePreview();
  };
  img.src = originalImageUrl.value;
};

// 绘制画布
const drawCanvas = () => {
  if (!cropCanvasRef.value || !originalImage.value) return;

  const canvas = cropCanvasRef.value;
  const ctx = canvas.getContext("2d");

  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 绘制图片
  ctx.save();
  ctx.translate(canvas.width / 2, canvas.height / 2);
  ctx.rotate((cropState.rotation * Math.PI) / 180);
  ctx.scale(cropState.scale, cropState.scale);

  // 计算图片在画布中的显示尺寸
  const imgWidth = canvas.width;
  const imgHeight = canvas.height;

  ctx.drawImage(
    originalImage.value,
    -imgWidth / 2,
    -imgHeight / 2,
    imgWidth,
    imgHeight
  );
  ctx.restore();

  // 绘制裁剪框
  drawCropBox(ctx);
};

// 绘制裁剪框
const drawCropBox = (ctx) => {
  const { cropX, cropY, cropWidth, cropHeight } = cropState;

  // 绘制遮罩
  ctx.fillStyle = "rgba(0, 0, 0, 0.5)";
  ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);

  // 清除裁剪区域
  ctx.globalCompositeOperation = "destination-out";
  ctx.fillRect(cropX, cropY, cropWidth, cropHeight);
  ctx.globalCompositeOperation = "source-over";

  // 绘制裁剪框边框
  ctx.strokeStyle = "#409eff";
  ctx.lineWidth = 2;
  ctx.strokeRect(cropX, cropY, cropWidth, cropHeight);

  // 绘制控制点
  const handleSize = 8;
  ctx.fillStyle = "#409eff";

  // 四个角的控制点
  const corners = [
    [cropX, cropY],
    [cropX + cropWidth, cropY],
    [cropX, cropY + cropHeight],
    [cropX + cropWidth, cropY + cropHeight],
  ];

  corners.forEach(([x, y]) => {
    ctx.fillRect(x - handleSize / 2, y - handleSize / 2, handleSize, handleSize);
  });
};

// 更新预览
const updatePreview = () => {
  if (!originalImage.value) return;

  drawCanvas();

  // 更新大预览
  updatePreviewCanvas(previewLargeRef.value, 100);
  // 更新中预览
  updatePreviewCanvas(previewMediumRef.value, 100);
};

// 更新预览画布
const updatePreviewCanvas = (canvas, size) => {
  if (!canvas || !originalImage.value || !cropCanvasRef.value) return;

  const ctx = canvas.getContext("2d");
  ctx.clearRect(0, 0, size, size);

  // 获取裁剪画布的实际显示尺寸
  const sourceCanvas = cropCanvasRef.value;

  // 计算从显示画布到原图的缩放比例
  const scaleX = originalImage.value.width / sourceCanvas.width;
  const scaleY = originalImage.value.height / sourceCanvas.height;

  // 计算裁剪区域在原图中的位置（考虑缩放和旋转）
  const centerX = sourceCanvas.width / 2;
  const centerY = sourceCanvas.height / 2;

  // 裁剪框相对于画布中心的偏移
  const offsetX = (cropState.cropX + cropState.cropWidth / 2) - centerX;
  const offsetY = (cropState.cropY + cropState.cropHeight / 2) - centerY;

  // 应用旋转变换到偏移量
  const cos = Math.cos(-cropState.rotation * Math.PI / 180);
  const sin = Math.sin(-cropState.rotation * Math.PI / 180);
  const rotatedOffsetX = offsetX * cos - offsetY * sin;
  const rotatedOffsetY = offsetX * sin + offsetY * cos;

  // 计算在原图中的实际位置
  const sourceX = (originalImage.value.width / 2) + (rotatedOffsetX * scaleX / cropState.scale) - (cropState.cropWidth * scaleX / cropState.scale / 2);
  const sourceY = (originalImage.value.height / 2) + (rotatedOffsetY * scaleY / cropState.scale) - (cropState.cropHeight * scaleY / cropState.scale / 2);
  const sourceWidth = cropState.cropWidth * scaleX / cropState.scale;
  const sourceHeight = cropState.cropHeight * scaleY / cropState.scale;

  // 确保裁剪区域不超出原图边界
  const clampedSourceX = Math.max(0, Math.min(sourceX, originalImage.value.width - sourceWidth));
  const clampedSourceY = Math.max(0, Math.min(sourceY, originalImage.value.height - sourceHeight));
  const clampedSourceWidth = Math.min(sourceWidth, originalImage.value.width - clampedSourceX);
  const clampedSourceHeight = Math.min(sourceHeight, originalImage.value.height - clampedSourceY);

  // 绘制裁剪后的图片
  ctx.drawImage(
    originalImage.value,
    clampedSourceX,
    clampedSourceY,
    clampedSourceWidth,
    clampedSourceHeight,
    0,
    0,
    size,
    size
  );
};

// 鼠标/触摸事件处理
const startCrop = (event) => {
  event.preventDefault();
  const rect = cropCanvasRef.value.getBoundingClientRect();
  const clientX = event.clientX || event.touches[0].clientX;
  const clientY = event.clientY || event.touches[0].clientY;

  cropState.startX = clientX - rect.left;
  cropState.startY = clientY - rect.top;
  cropState.isDragging = true;
};

const updateCrop = (event) => {
  if (!cropState.isDragging) return;

  event.preventDefault();
  const rect = cropCanvasRef.value.getBoundingClientRect();
  const clientX = event.clientX || event.touches[0].clientX;
  const clientY = event.clientY || event.touches[0].clientY;

  const currentX = clientX - rect.left;
  const currentY = clientY - rect.top;

  const deltaX = currentX - cropState.startX;
  const deltaY = currentY - cropState.startY;

  // 移动裁剪框
  cropState.cropX = Math.max(0, Math.min(cropState.cropX + deltaX, cropCanvasRef.value.width - cropState.cropWidth));
  cropState.cropY = Math.max(0, Math.min(cropState.cropY + deltaY, cropCanvasRef.value.height - cropState.cropHeight));

  cropState.startX = currentX;
  cropState.startY = currentY;

  updatePreview();
};

const endCrop = () => {
  cropState.isDragging = false;
};

// 旋转功能
const rotate = (angle) => {
  cropState.rotation += angle;
  updatePreview();
};

// 重置裁剪
const resetCrop = () => {
  if (!cropCanvasRef.value) return;

  const canvas = cropCanvasRef.value;
  const size = Math.min(canvas.width, canvas.height) * 0.6;

  cropState.cropX = (canvas.width - size) / 2;
  cropState.cropY = (canvas.height - size) / 2;
  cropState.cropWidth = size;
  cropState.cropHeight = size;
  cropState.scale = 1;
  cropState.rotation = 0;

  updatePreview();
};

// 确认裁剪
const handleConfirm = async () => {
  if (!originalImage.value || !cropCanvasRef.value) return;

  processing.value = true;

  try {
    // 创建输出画布
    const outputCanvas = document.createElement("canvas");
    const outputCtx = outputCanvas.getContext("2d");

    outputCanvas.width = props.outputSize;
    outputCanvas.height = props.outputSize;

    // 获取裁剪画布的实际显示尺寸
    const sourceCanvas = cropCanvasRef.value;

    // 计算从显示画布到原图的缩放比例
    const scaleX = originalImage.value.width / sourceCanvas.width;
    const scaleY = originalImage.value.height / sourceCanvas.height;

    // 使用与预览相同的计算逻辑
    const centerX = sourceCanvas.width / 2;
    const centerY = sourceCanvas.height / 2;

    // 裁剪框相对于画布中心的偏移
    const offsetX = (cropState.cropX + cropState.cropWidth / 2) - centerX;
    const offsetY = (cropState.cropY + cropState.cropHeight / 2) - centerY;

    // 应用旋转变换到偏移量
    const cos = Math.cos(-cropState.rotation * Math.PI / 180);
    const sin = Math.sin(-cropState.rotation * Math.PI / 180);
    const rotatedOffsetX = offsetX * cos - offsetY * sin;
    const rotatedOffsetY = offsetX * sin + offsetY * cos;

    // 计算在原图中的实际位置
    const sourceX = (originalImage.value.width / 2) + (rotatedOffsetX * scaleX / cropState.scale) - (cropState.cropWidth * scaleX / cropState.scale / 2);
    const sourceY = (originalImage.value.height / 2) + (rotatedOffsetY * scaleY / cropState.scale) - (cropState.cropHeight * scaleY / cropState.scale / 2);
    const sourceWidth = cropState.cropWidth * scaleX / cropState.scale;
    const sourceHeight = cropState.cropHeight * scaleY / cropState.scale;

    // 确保裁剪区域不超出原图边界
    const clampedSourceX = Math.max(0, Math.min(sourceX, originalImage.value.width - sourceWidth));
    const clampedSourceY = Math.max(0, Math.min(sourceY, originalImage.value.height - sourceHeight));
    const clampedSourceWidth = Math.min(sourceWidth, originalImage.value.width - clampedSourceX);
    const clampedSourceHeight = Math.min(sourceHeight, originalImage.value.height - clampedSourceY);

    // 绘制最终图片（不再应用旋转，因为已经在坐标计算中处理了）
    outputCtx.drawImage(
      originalImage.value,
      clampedSourceX,
      clampedSourceY,
      clampedSourceWidth,
      clampedSourceHeight,
      0,
      0,
      props.outputSize,
      props.outputSize
    );

    // 转换为Blob
    const blob = await new Promise((resolve, reject) => {
      outputCanvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error("图片处理失败"));
          }
        },
        props.outputFormat,
        props.outputQuality
      );
    });

    // 生成预览URL
    const previewUrl = URL.createObjectURL(blob);
    currentAvatar.value = previewUrl;

    // 触发事件
    emit("update:modelValue", previewUrl);
    emit("crop-complete", {
      blob,
      canvas: outputCanvas,
      previewUrl,
      file: blob,
      size: blob.size,
    });

    cropDialogVisible.value = false;
    ElMessage.success("头像裁剪完成！");
  } catch (error) {
    console.error("裁剪失败:", error);
    ElMessage.error("裁剪失败: " + error.message);
  } finally {
    processing.value = false;
  }
};

// 取消裁剪
const handleCancel = () => {
  cropDialogVisible.value = false;
  emit("crop-cancel");
};

// 对话框关闭处理
const handleDialogClose = () => {
  if (originalImageUrl.value) {
    URL.revokeObjectURL(originalImageUrl.value);
    originalImageUrl.value = "";
  }
  originalImage.value = null;
};

// 组件卸载时清理
onBeforeUnmount(() => {
  if (originalImageUrl.value) {
    URL.revokeObjectURL(originalImageUrl.value);
  }
  if (currentAvatar.value && currentAvatar.value.startsWith("blob:")) {
    URL.revokeObjectURL(currentAvatar.value);
  }
});
</script>

<style scoped>
.avatar-uploader {
  display: inline-block;
}

.avatar-display {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.avatar-display:hover {
  transform: translateY(-2px);
}

.avatar-container {
  position: relative;
  width: v-bind('avatarSize + "px"');
  height: v-bind('avatarSize + "px"');
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #ffffff;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.avatar-container:hover {
  border-color: #409eff;
  box-shadow:
    0 8px 25px rgba(64, 158, 255, 0.2),
    0 4px 10px rgba(0, 0, 0, 0.1);
  transform: scale(1.02);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #6c757d;
  font-size: 13px;
  font-weight: 500;
}

.placeholder-text {
  margin-top: 8px;
  text-align: center;
  line-height: 1.3;
  font-weight: 400;
  opacity: 0.8;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(64, 158, 255, 0.95) 0%,
    rgba(103, 194, 58, 0.95) 100%
  );
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 12px;
  font-weight: 500;
  pointer-events: none;
  backdrop-filter: blur(4px);
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.overlay-text {
  margin-top: 4px;
  text-align: center;
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* 对话框样式 */
.avatar-crop-dialog {
  --el-dialog-padding-primary: 0;
}

.avatar-crop-dialog :deep(.el-dialog) {
  margin: 2vh auto;
  border-radius: 16px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 10px 20px rgba(0, 0, 0, 0.1);
  max-width: 95vw;
  max-height: 90vh;
  overflow: hidden;
}

.avatar-crop-dialog :deep(.el-dialog__header) {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.avatar-crop-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.avatar-crop-dialog :deep(.el-dialog__headerbtn) {
  top: 20px;
  right: 20px;
}

.avatar-crop-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}

.avatar-crop-dialog :deep(.el-dialog__body) {
  padding: 0;
  height: 500px;
  overflow: hidden;
  background: #f8fafc;
}

.crop-container {
  display: grid;
  grid-template-columns: 1.5fr 1fr;
  height: 100%;
  min-height: 500px;
}

.crop-section {
  display: flex;
  flex-direction: column;
  background: white;
  border-right: 1px solid #e2e8f0;
}

.crop-area {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

.crop-canvas {
  display: block;
  cursor: move;
  border-radius: 8px;
  border: 2px solid #e2e8f0;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  max-height: 100%;
}

.crop-tools {
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #f1f5f9;
  display: flex;
  gap: 20px;
  align-items: center;
}

.tool-group {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.tool-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  min-width: 40px;
}

.scale-slider {
  flex: 1;
}

.rotation-controls {
  display: flex;
  gap: 8px;
}

.preview-section {
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  padding: 20px;
}

.preview-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.preview-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
  justify-content: center;
  flex: 1;
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.preview-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.preview-circle {
  border-radius: 50%;
  border: 2px solid #e2e8f0;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-circle.large {
  width: 100px;
  height: 100px;
}

.preview-circle.medium {
  width: 100px;
  height: 100px;
  border-radius: 8px;
}

.preview-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.preview-size {
  font-size: 12px;
  color: #6b7280;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-top: 1px solid #e2e8f0;
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .avatar-crop-dialog :deep(.el-dialog) {
    width: 95vw !important;
    margin: 1vh auto;
  }

  .crop-container {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
    height: auto;
  }

  .avatar-crop-dialog :deep(.el-dialog__body) {
    height: auto;
    max-height: 85vh;
    overflow-y: auto;
  }

  .crop-section {
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }

  .crop-area {
    height: 300px;
  }

  .preview-grid {
    flex-direction: row;
    justify-content: center;
    padding: 16px 0;
  }

  .preview-item {
    flex: 1;
    max-width: 140px;
  }

  .crop-tools {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .tool-group {
    justify-content: space-between;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .footer-actions {
    justify-content: center;
  }
}

@media (max-width: 640px) {
  .avatar-crop-dialog :deep(.el-dialog__header) {
    padding: 16px;
  }

  .crop-area {
    padding: 16px;
    height: 250px;
  }

  .crop-tools {
    padding: 16px;
  }

  .preview-section {
    padding: 16px;
  }

  .preview-grid {
    flex-direction: column;
    gap: 12px;
  }

  .dialog-footer {
    padding: 16px;
  }

  .footer-actions .el-button {
    flex: 1;
  }
}

/* 禁用状态 */
.avatar-uploader.disabled .avatar-display {
  cursor: not-allowed;
  opacity: 0.6;
}

.avatar-uploader.disabled .avatar-container:hover {
  transform: none;
  border-color: #e4e7ed;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.avatar-uploader.disabled .avatar-overlay {
  display: none;
}
</style>
