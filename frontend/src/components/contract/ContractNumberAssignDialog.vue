<template>
  <el-dialog
    v-model="dialogVisible"
    title="分配合同编号"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div v-if="contract" class="assign-dialog-content">
      <!-- 合同信息 -->
      <div class="contract-info">
        <h3>合同信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="流水号">
            {{ contract.serial_number }}
          </el-descriptions-item>
          <el-descriptions-item label="合同文件">
            {{ contract.filename }}
          </el-descriptions-item>
          <el-descriptions-item label="提交人">
            {{ contract.submitter_name }}
          </el-descriptions-item>
          <el-descriptions-item label="审核完成时间">
            {{ formatDateTime(contract.reviewed_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="当前状态" :span="2">
            <el-tag :type="getStatusColor(contract.status)">
              {{ formatStatus(contract.status) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 编号分配表单 -->
      <div class="assign-form">
        <h3>分配合同编号</h3>
        <el-form
          ref="assignFormRef"
          :model="assignForm"
          :rules="assignRules"
          label-width="100px"
          size="default"
        >
          <el-form-item label="合同编号" prop="contract_number" required>
            <el-input
              v-model="assignForm.contract_number"
              placeholder="请输入合同编号"
              maxlength="50"
              show-word-limit
              clearable
            />
            <div class="form-tips">
              <el-icon><InfoFilled /></el-icon>
              <span>请输入唯一的合同编号，建议格式：HT-YYYY-NNNN</span>
            </div>
          </el-form-item>

          <el-form-item label="备注说明" prop="comment">
            <el-input
              v-model="assignForm.comment"
              type="textarea"
              :rows="3"
              placeholder="可选：添加备注说明"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="submitting" @click="handleClose">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="submitting"
          :disabled="!assignForm.contract_number"
          @click="submitAssignment"
        >
          {{ submitting ? "分配中..." : "确认分配" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";

import { contractsAPI } from "@/api/contracts";
import { formatDateTime } from "@/utils/dateUtils";
import { STATUS_LABELS, STATUS_COLORS } from "@/utils/contractStatus";
import { useContracts } from "@/composables/useContracts";

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  contract: {
    type: Object,
    default: null,
  },
});

// Emits
const emit = defineEmits(["update:modelValue", "assigned"]);

// 响应式数据
const assignFormRef = ref();
const submitting = ref(false);

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 分配表单
const assignForm = reactive({
  contract_number: "",
  comment: "",
});

// 表单验证规则
const assignRules = {
  contract_number: [
    { required: true, message: "请输入合同编号", trigger: "blur" },
    { min: 1, max: 50, message: "合同编号长度应在1-50字符之间", trigger: "blur" },
    {
      pattern: /^[A-Za-z0-9\-_]+$/,
      message: "合同编号只能包含字母、数字、横线和下划线",
      trigger: "blur",
    },
  ],
  comment: [
    { max: 500, message: "备注不能超过500字符", trigger: "blur" },
  ],
};

// 监听对话框打开，重置表单
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      resetForm();
      generateSuggestedNumber();
    }
  }
);

// 重置表单
const resetForm = () => {
  assignForm.contract_number = "";
  assignForm.comment = "";
  if (assignFormRef.value) {
    assignFormRef.value.clearValidate();
  }
};

// 生成建议的合同编号
const generateSuggestedNumber = () => {
  if (props.contract) {
    const year = new Date().getFullYear();
    const serialPart = props.contract.serial_number.replace(/\D/g, "");
    assignForm.contract_number = `HT-${year}-${serialPart.padStart(4, "0")}`;
  }
};

// 提交分配
const submitAssignment = async () => {
  try {
    // 验证表单
    await assignFormRef.value.validate();

    await ElMessageBox.confirm(
      `确定要为合同 ${props.contract.serial_number} 分配编号 ${assignForm.contract_number} 吗？`,
      "确认分配合同编号",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    submitting.value = true;

    const response = await contractsAPI.assignContractNumber(
      props.contract.id,
      {
        contract_number: assignForm.contract_number,
        comment: assignForm.comment,
      }
    );

    if (response.success) {
      ElMessage.success("合同编号分配成功");

      // 强制刷新缓存 - 类似审核员审核后的处理
      try {
        // 触发父组件刷新数据
        emit("assigned", response.data);

        // 如果使用了 useContracts composable，也刷新全局数据
        const { getContracts, getStats } = useContracts();
        await Promise.all([getContracts(), getStats()]);
      } catch (refreshError) {
        if (import.meta.env.DEV) {
          console.error("刷新数据失败:", refreshError);
        }
        // 静默处理刷新失败，不影响用户体验
      }

      dialogVisible.value = false;
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("分配合同编号失败:", error);
      ElMessage.error(error.message || "分配合同编号失败");
    }
  } finally {
    submitting.value = false;
  }
};

// 关闭对话框
const handleClose = () => {
  if (!submitting.value) {
    dialogVisible.value = false;
  }
};

// 格式化状态
const formatStatus = (status) => {
  return STATUS_LABELS[status] || status;
};

// 获取状态颜色
const getStatusColor = (status) => {
  return STATUS_COLORS[status] || "info";
};
</script>

<style scoped>
.assign-dialog-content {
  padding: 0;
}

.contract-info {
  margin-bottom: 24px;
}

.contract-info h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.assign-form h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.form-tips {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.form-tips .el-icon {
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
