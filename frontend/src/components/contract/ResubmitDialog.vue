<template>
  <el-dialog
    v-model="dialogVisible"
    title="重新提交合同"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div v-if="contract" class="resubmit-content">
      <!-- 拒绝原因显示 -->
      <div class="rejection-info">
        <h4 class="section-title">
          <el-icon class="title-icon"><WarningFilled /></el-icon>
          审核意见
        </h4>
        <div class="rejection-card">
          <div class="rejection-header">
            <span class="rejection-label">拒绝原因：</span>
            <el-tag type="danger" size="small">已拒绝</el-tag>
          </div>
          <div class="rejection-comment">
            {{ contract.review_comment || "无具体说明" }}
          </div>
          <div class="rejection-time">
            审核时间：{{ formatDateTime(contract.reviewed_at) }}
          </div>
        </div>
      </div>

      <!-- 重新提交表单 -->
      <div class="resubmit-form">
        <h4 class="section-title">
          <el-icon class="title-icon"><EditPen /></el-icon>
          重新提交
        </h4>

        <el-form
          ref="resubmitFormRef"
          :model="resubmitForm"
          :rules="resubmitRules"
          label-width="120px"
          size="default"
        >
          <!-- 文件重新上传 -->
          <el-form-item label="合同文件" prop="files">
            <div class="current-file">
              <div class="file-info">
                <el-icon class="file-icon"><DocumentCopy /></el-icon>
                <div class="file-details">
                  <span class="file-name">{{ contract.filename }}</span>
                  <span class="file-size">{{
                    formatFileSize(contract.file_size)
                  }}</span>
                </div>
              </div>
              <el-button size="small" @click="showFileUpload = !showFileUpload">
                {{ showFileUpload ? "取消更换" : "更换文件" }}
              </el-button>
            </div>

            <div v-if="showFileUpload" class="file-upload-section">
              <FileUpload
                v-model="resubmitForm.files"
                :limit="1"
                accept=".pdf"
                :max-size="10 * 1024 * 1024"
                @success="handleFileUploadSuccess"
                @error="handleFileUploadError"
              />
            </div>
          </el-form-item>

          <!-- 审核人员 -->
          <el-form-item label="审核人员" prop="reviewer_id">
            <el-select
              v-model="resubmitForm.reviewer_id"
              placeholder="请选择审核人员"
              style="width: 100%"
              :loading="loadingReviewers"
            >
              <el-option
                v-for="reviewer in reviewers"
                :key="reviewer.id"
                :label="`${reviewer.username} (${reviewer.role === 'admin' ? '管理员' : '审核员'})`"
                :value="reviewer.id"
              />
            </el-select>
          </el-form-item>

          <!-- 修改说明 -->
          <el-form-item label="修改说明" prop="submit_note" required>
            <el-input
              v-model="resubmitForm.submit_note"
              type="textarea"
              :rows="4"
              placeholder="请说明本次修改的内容和改进措施"
              maxlength="500"
              show-word-limit
            />
            <div class="form-tips">
              <el-icon><InfoFilled /></el-icon>
              <span>请详细说明针对审核意见所做的修改</span>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="submitting" @click="handleClose">
          取消
        </el-button>
        <el-button type="primary" :loading="submitting" @click="handleResubmit">
          {{ submitting ? "提交中..." : "重新提交" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  WarningFilled,
  EditPen,
  DocumentCopy,
  InfoFilled,
} from "@element-plus/icons-vue";

import FileUpload from "@/components/common/FileUpload.vue";
import { useContracts } from "@/composables/useContracts";
import { contractsAPI } from "@/api/contracts";
import { getContractResubmitRules } from "@/utils/validationRules";

// 定义 props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  contract: {
    type: Object,
    default: null,
  },
});

// 定义 emits
const emit = defineEmits(["update:modelValue", "resubmitted"]);

// 合同管理
const { resubmitContract, submitting, formatDateTime, formatFileSize } =
  useContracts();

// 对话框显示状态
const dialogVisible = ref(props.modelValue);

// 表单引用
const resubmitFormRef = ref();

// 是否显示文件上传
const showFileUpload = ref(false);

// 审核人员列表
const reviewers = ref([]);
const loadingReviewers = ref(false);

// 重新提交表单数据
const resubmitForm = reactive({
  files: [],
  reviewer_id: "",
  submit_note: "",
});

// 表单验证规则 - 使用统一的验证规则
const resubmitRules = getContractResubmitRules();

// 监听 props 变化
watch(
  () => props.modelValue,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);

watch(
  () => props.contract,
  (newContract) => {
    if (newContract) {
      initForm();
    }
  },
);

// 监听对话框显示状态
watch(
  () => dialogVisible.value,
  (newValue) => {
    emit("update:modelValue", newValue);

    if (newValue && props.contract) {
      initForm();
      getReviewers();
    } else {
      resetForm();
    }
  },
);

// 初始化表单
const initForm = () => {
  if (props.contract) {
    resubmitForm.reviewer_id = props.contract.reviewer_id;
    resubmitForm.submit_note = "";
    resubmitForm.files = [];
    showFileUpload.value = false;
  }
};

// 重置表单
const resetForm = () => {
  if (resubmitFormRef.value) {
    resubmitFormRef.value.resetFields();
  }

  resubmitForm.files = [];
  resubmitForm.reviewer_id = "";
  resubmitForm.submit_note = "";
  showFileUpload.value = false;
};

// 获取审核人员列表
const getReviewers = async () => {
  try {
    loadingReviewers.value = true;
    const response = await contractsAPI.getReviewers();
    if (response.success) {
      reviewers.value = response.data;
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("获取审核人员失败:", error);
    }
    ElMessage.error("获取审核人员失败");
  } finally {
    loadingReviewers.value = false;
  }
};

// 处理文件上传成功
const handleFileUploadSuccess = (fileInfo) => {};

// 处理文件上传失败
const handleFileUploadError = (error) => {
  if (import.meta.env.DEV) {
    console.error("文件上传失败:", error);
  }
};

// 处理重新提交
const handleResubmit = async () => {
  try {
    // 验证表单
    await resubmitFormRef.value.validate();

    // 构建提交数据
    const submitData = {
      reviewer_id: resubmitForm.reviewer_id,
      submit_note: resubmitForm.submit_note,
    };

    // 如果上传了新文件，使用新文件信息
    if (resubmitForm.files.length > 0) {
      const file = resubmitForm.files[0];
      submitData.filename = file.name;
      submitData.file_path = file.filename;
      submitData.file_size = file.size;
    }

    // 重新提交合同
    const result = await resubmitContract(props.contract.id, submitData);

    if (result) {
      emit("resubmitted", result);
      dialogVisible.value = false;
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("重新提交失败:", error);
    }
  }
};

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false;
};

// 组件挂载时获取审核人员
onMounted(() => {
  if (dialogVisible.value && props.contract) {
    getReviewers();
  }
});
</script>

<style scoped>
.resubmit-content {
  max-height: 70vh;
  overflow-y: auto;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px;
}

.title-icon {
  color: #409eff;
}

.rejection-info {
  margin-bottom: 32px;
}

.rejection-card {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 8px;
  padding: 16px;
}

.rejection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.rejection-label {
  font-weight: 500;
  color: #f56c6c;
}

.rejection-comment {
  background: #fff;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  color: #303133;
  line-height: 1.6;
  border: 1px solid #f5c6cb;
}

.rejection-time {
  font-size: 12px;
  color: #909399;
}

.current-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 12px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 20px;
  color: #409eff;
}

.file-details {
  display: flex;
  flex-direction: column;
}

.file-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-upload-section {
  margin-top: 12px;
  padding: 16px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}

.form-tips {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.form-tips .el-icon {
  color: #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Element Plus 样式覆盖 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .current-file {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .rejection-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}
</style>
