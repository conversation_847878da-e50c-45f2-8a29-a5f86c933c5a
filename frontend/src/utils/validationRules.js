/**
 * 统一表单验证规则
 * 提供常用的表单验证规则，避免重复定义
 */

/**
 * 通用验证规则
 */
export const commonRules = {
  // 用户名验证规则
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      min: 3,
      max: 50,
      message: "用户名长度必须在3-50个字符之间",
      trigger: "blur",
    },
    {
      pattern: /^[a-zA-Z0-9_]+$/,
      message: "用户名只能包含字母、数字和下划线",
      trigger: "blur",
    },
  ],

  // 密码验证规则
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码长度至少6个字符", trigger: "blur" },
  ],

  // 邮箱验证规则
  email: [
    { type: "email", message: "邮箱格式不正确", trigger: "blur" },
  ],

  // 手机号验证规则
  phone: [
    { 
      pattern: /^1[3-9]\d{9}$/, 
      message: "手机号格式不正确", 
      trigger: "blur" 
    },
  ],

  // 真实姓名验证规则
  realName: [
    { max: 100, message: "真实姓名不能超过100个字符", trigger: "blur" },
  ],

  // 角色验证规则
  role: [
    { required: true, message: "请选择角色", trigger: "change" }
  ],

  // 审核级别验证规则
  reviewLevel: [
    { required: true, message: "请选择审核级别", trigger: "change" },
  ],

  // 审核员验证规则
  reviewerId: [
    { required: true, message: "请选择审核员", trigger: "change" },
  ],

  // 文件上传验证规则
  files: [
    { required: true, message: "请上传合同文件", trigger: "change" }
  ],

  // 提交说明验证规则
  submitNote: [
    { max: 500, message: "提交说明不能超过500个字符", trigger: "blur" },
  ],

  // 修改说明验证规则（重新提交时）
  resubmitNote: [
    { required: true, message: "请填写修改说明", trigger: "blur" },
    { min: 10, message: "修改说明至少10个字符", trigger: "blur" },
    { max: 500, message: "修改说明不能超过500个字符", trigger: "blur" },
  ],
};

/**
 * 创建自定义验证规则
 */
export const createCustomRules = {
  /**
   * 创建必填验证规则
   * @param {string} message - 错误消息
   * @param {string} trigger - 触发方式
   */
  required(message, trigger = "blur") {
    return { required: true, message, trigger };
  },

  /**
   * 创建长度验证规则
   * @param {number} min - 最小长度
   * @param {number} max - 最大长度
   * @param {string} trigger - 触发方式
   */
  length(min, max, trigger = "blur") {
    return {
      min,
      max,
      message: `长度必须在${min}-${max}个字符之间`,
      trigger,
    };
  },

  /**
   * 创建正则验证规则
   * @param {RegExp} pattern - 正则表达式
   * @param {string} message - 错误消息
   * @param {string} trigger - 触发方式
   */
  pattern(pattern, message, trigger = "blur") {
    return { pattern, message, trigger };
  },
};

/**
 * 合并验证规则
 * @param {...Array} rules - 要合并的规则数组
 * @returns {Array} 合并后的规则数组
 */
export const mergeRules = (...rules) => {
  return rules.flat();
};

/**
 * 获取用户表单验证规则
 * @param {boolean} isEdit - 是否为编辑模式
 * @returns {Object} 验证规则对象
 */
export const getUserFormRules = (isEdit = false) => {
  const rules = {
    username: commonRules.username,
    role: commonRules.role,
    real_name: commonRules.realName,
    email: commonRules.email,
    phone: commonRules.phone,
  };

  // 编辑模式下不需要密码验证
  if (!isEdit) {
    rules.password = commonRules.password;
  }

  return rules;
};

/**
 * 获取合同提交表单验证规则
 * @returns {Object} 验证规则对象
 */
export const getContractSubmitRules = () => {
  return {
    files: commonRules.files,
    review_level: commonRules.reviewLevel,
    reviewer_id: commonRules.reviewerId,
    submit_note: commonRules.submitNote,
  };
};

/**
 * 获取合同重新提交表单验证规则
 * @returns {Object} 验证规则对象
 */
export const getContractResubmitRules = () => {
  return {
    reviewer_id: commonRules.reviewerId,
    submit_note: commonRules.resubmitNote,
  };
};

export default {
  commonRules,
  createCustomRules,
  mergeRules,
  getUserFormRules,
  getContractSubmitRules,
  getContractResubmitRules,
};
